export const presaleV2Abi = [
  {
    type: 'constructor',
    inputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'receive',
    stateMutability: 'payable',
  },
  {
    type: 'function',
    name: 'addRound',
    inputs: [
      {
        name: 'round',
        type: 'tuple',
        internalType: 'struct Round',
        components: [
          {
            name: 'name',
            type: 'string',
            internalType: 'string',
          },
          {
            name: 'startTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'endTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'whitelistRoot',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'proofsUri',
            type: 'string',
            internalType: 'string',
          },
        ],
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'beneficiary',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'extend',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amountA',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'buy',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amountA',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'attributes',
        type: 'tuple',
        internalType: 'struct IMembership.Attributes',
        components: [
          {
            name: 'price',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'allocation',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'claimableBackUnit',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'tgeNumerator',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'tgeDenominator',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'cliffDuration',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'cliffNumerator',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'cliffDenominator',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'vestingPeriodCount',
            type: 'uint32',
            internalType: 'uint32',
          },
          {
            name: 'vestingPeriodDuration',
            type: 'uint32',
            internalType: 'uint32',
          },
        ],
      },
      {
        name: 'proof',
        type: 'bytes32[]',
        internalType: 'bytes32[]',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'claim',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'claimback',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'amountA',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: 'newPublicId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'claimbackPeriod',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'depositTokenA',
    inputs: [
      {
        name: 'amountA',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'getFeeCollector',
    inputs: [],
    outputs: [
      {
        name: 'feeCollector_',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'getFees',
    inputs: [],
    outputs: [
      {
        name: 'fees_',
        type: 'tuple',
        internalType: 'struct Fees',
        components: [
          {
            name: 'tokenAFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenAFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenBFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenBFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'nftFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'nftFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
        ],
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'getRound',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'tuple',
        internalType: 'struct Round',
        components: [
          {
            name: 'name',
            type: 'string',
            internalType: 'string',
          },
          {
            name: 'startTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'endTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'whitelistRoot',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'proofsUri',
            type: 'string',
            internalType: 'string',
          },
        ],
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'getRoundState',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'uint8',
        internalType: 'enum RoundState',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'getRounds',
    inputs: [],
    outputs: [
      {
        name: 'ids',
        type: 'uint256[]',
        internalType: 'uint256[]',
      },
      {
        name: 'rounds_',
        type: 'tuple[]',
        internalType: 'struct Round[]',
        components: [
          {
            name: 'name',
            type: 'string',
            internalType: 'string',
          },
          {
            name: 'startTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'endTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'whitelistRoot',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'proofsUri',
            type: 'string',
            internalType: 'string',
          },
        ],
      },
      {
        name: 'states',
        type: 'uint8[]',
        internalType: 'enum RoundState[]',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'getTgeTimestamp',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'initialize',
    inputs: [
      {
        name: 'configuration',
        type: 'tuple',
        internalType: 'struct Configuration',
        components: [
          {
            name: 'tokenA',
            type: 'address',
            internalType: 'contract IERC20',
          },
          {
            name: 'tokenB',
            type: 'address',
            internalType: 'contract IERC20',
          },
          {
            name: 'manager',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'beneficiary',
            type: 'address',
            internalType: 'address',
          },
          {
            name: 'listingTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'claimbackPeriod',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'membership',
            type: 'tuple',
            internalType: 'struct MembershipConfiguration',
            components: [
              {
                name: 'factory',
                type: 'address',
                internalType: 'address',
              },
              {
                name: 'descriptor',
                type: 'address',
                internalType: 'contract IMembershipDescriptor',
              },
              {
                name: 'metadata',
                type: 'tuple',
                internalType: 'struct IMembership.Metadata',
                components: [
                  {
                    name: 'token',
                    type: 'address',
                    internalType: 'address',
                  },
                  {
                    name: 'color',
                    type: 'string',
                    internalType: 'string',
                  },
                  {
                    name: 'description',
                    type: 'string',
                    internalType: 'string',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        name: 'rounds_',
        type: 'tuple[]',
        internalType: 'struct Round[]',
        components: [
          {
            name: 'name',
            type: 'string',
            internalType: 'string',
          },
          {
            name: 'startTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'endTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'whitelistRoot',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'proofsUri',
            type: 'string',
            internalType: 'string',
          },
        ],
      },
      {
        name: 'fees_',
        type: 'tuple',
        internalType: 'struct Fees',
        components: [
          {
            name: 'tokenAFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenAFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenBFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'tokenBFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'nftFeeNumerator',
            type: 'uint16',
            internalType: 'uint16',
          },
          {
            name: 'nftFeeDenominator',
            type: 'uint16',
            internalType: 'uint16',
          },
        ],
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'liquidityA',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'liquidityB',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'listingTimestamp',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'manager',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'membership',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IMembership',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'nonClaimableBackTokenB',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'parentVest',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IVest',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'removeRound',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'roundParticipants',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: '',
        type: 'address',
        internalType: 'address',
      },
    ],
    outputs: [
      {
        name: '',
        type: 'bool',
        internalType: 'bool',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'tokenA',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IERC20',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'tokenB',
    inputs: [],
    outputs: [
      {
        name: '',
        type: 'address',
        internalType: 'contract IERC20',
      },
    ],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'updateListingTimestamp',
    inputs: [
      {
        name: 'listingTimestamp_',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'updateRound',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'round',
        type: 'tuple',
        internalType: 'struct Round',
        components: [
          {
            name: 'name',
            type: 'string',
            internalType: 'string',
          },
          {
            name: 'startTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'endTimestamp',
            type: 'uint256',
            internalType: 'uint256',
          },
          {
            name: 'whitelistRoot',
            type: 'bytes32',
            internalType: 'bytes32',
          },
          {
            name: 'proofsUri',
            type: 'string',
            internalType: 'string',
          },
        ],
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'updateWhitelist',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'whitelistRoot',
        type: 'bytes32',
        internalType: 'bytes32',
      },
      {
        name: 'proofsUri',
        type: 'string',
        internalType: 'string',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'withdrawCoin',
    inputs: [
      {
        name: 'to',
        type: 'address',
        internalType: 'address payable',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'withdrawToken',
    inputs: [
      {
        name: 'to',
        type: 'address',
        internalType: 'address',
      },
      {
        name: 'token',
        type: 'address',
        internalType: 'contract IERC20',
      },
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'withdrawTokenA',
    inputs: [
      {
        name: 'amount',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'withdrawTokenB',
    inputs: [],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'event',
    name: 'Claimbacked',
    inputs: [
      {
        name: 'vMembershipId',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'amountA',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'Claimed',
    inputs: [
      {
        name: 'vMembershipId',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
      {
        name: 'amountA',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'ClaimedWithSaleMembership',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'DepositedA',
    inputs: [
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'Initialized',
    inputs: [
      {
        name: 'version',
        type: 'uint64',
        indexed: false,
        internalType: 'uint64',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'ListingTimestampUpdated',
    inputs: [
      {
        name: 'timestamp',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'RoundUpdated',
    inputs: [
      {
        name: 'id',
        type: 'uint256',
        indexed: true,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'WithdrawnA',
    inputs: [
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'event',
    name: 'WithdrawnB',
    inputs: [
      {
        name: 'amount',
        type: 'uint256',
        indexed: false,
        internalType: 'uint256',
      },
    ],
    anonymous: false,
  },
  {
    type: 'error',
    name: 'AccountMismatch',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'AddressEmptyCode',
    inputs: [
      {
        name: 'target',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'AddressInsufficientBalance',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'ClaimNotAllowed',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    type: 'error',
    name: 'ClaimbackNotAllowed',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    type: 'error',
    name: 'CliffHeightWithoutSubsequentUnlocks',
    inputs: [],
  },
  {
    type: 'error',
    name: 'CliffLikeVesting',
    inputs: [],
  },
  {
    type: 'error',
    name: 'CliffWithImmediateUnlock',
    inputs: [],
  },
  {
    type: 'error',
    name: 'DenominatorZero',
    inputs: [],
  },
  {
    type: 'error',
    name: 'FailedInnerCall',
    inputs: [],
  },
  {
    type: 'error',
    name: 'InvalidDeployer',
    inputs: [],
  },
  {
    type: 'error',
    name: 'InvalidInitialization',
    inputs: [],
  },
  {
    type: 'error',
    name: 'MembershipUsed',
    inputs: [
      {
        name: 'membershipId',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    type: 'error',
    name: 'NotInitializing',
    inputs: [],
  },
  {
    type: 'error',
    name: 'ProofsUsedUp',
    inputs: [
      {
        name: 'roundId',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'whitelistedAddress',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'RoundIsLocked',
    inputs: [
      {
        name: 'id',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    type: 'error',
    name: 'RoundNotExists',
    inputs: [
      {
        name: 'id',
        type: 'uint256',
        internalType: 'uint256',
      },
    ],
  },
  {
    type: 'error',
    name: 'RoundStateMismatch',
    inputs: [
      {
        name: 'id',
        type: 'uint256',
        internalType: 'uint256',
      },
      {
        name: 'current',
        type: 'uint8',
        internalType: 'enum RoundState',
      },
      {
        name: 'expected',
        type: 'uint8',
        internalType: 'enum RoundState',
      },
    ],
  },
  {
    type: 'error',
    name: 'SafeERC20FailedOperation',
    inputs: [
      {
        name: 'token',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'TokenWithTransferFees',
    inputs: [
      {
        name: 'tokenAddress',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'UnacceptableListingTimestamp',
    inputs: [],
  },
  {
    type: 'error',
    name: 'UnacceptableReference',
    inputs: [],
  },
  {
    type: 'error',
    name: 'UnacceptableValue',
    inputs: [],
  },
  {
    type: 'error',
    name: 'Unauthorized',
    inputs: [
      {
        name: 'account',
        type: 'address',
        internalType: 'address',
      },
    ],
  },
  {
    type: 'error',
    name: 'VestingSize',
    inputs: [],
  },
  {
    type: 'error',
    name: 'VestingWithImmediateUnlock',
    inputs: [],
  },
  {
    type: 'error',
    name: 'VestingWithoutUnlocks',
    inputs: [],
  },
  {
    type: 'error',
    name: 'WithdrawToZeroAddress',
    inputs: [],
  },
] as const;
