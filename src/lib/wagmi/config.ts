import { bsc, bscTestnet, foundry } from 'viem/chains';
import { createConfig, http } from 'wagmi';
import { injected, walletConnect } from 'wagmi/connectors';

import { supportedChains } from './supported-chains';

export const config = (walletConnectProjectId: string) => {
  return createConfig({
    chains: supportedChains,
    connectors: [
      injected({
        target: 'metaMask',
      }),
      walletConnect({
        projectId: walletConnectProjectId,
      }),
    ],
    transports: {
      [bsc.id]: http(),
      [foundry.id]: http(),
      [bscTestnet.id]: http(),
    },
  });
};

export type WagmiConfig = typeof config;

declare module 'wagmi' {
  interface Register {
    config: typeof config;
  }
}
