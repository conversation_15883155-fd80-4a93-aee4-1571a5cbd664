export function lsGet<T>(key: string, type: 'string' | 'number' | 'object'): T {
  const value = localStorage.getItem(key);

  switch (type) {
    case 'string':
      return value ? (value as unknown as T) : (null as unknown as T);
    case 'number':
      return value ? (parseInt(value) as unknown as T) : (0 as unknown as T);
    case 'object':
      return value ? (JSON.parse(value) as unknown as T) : (null as unknown as T);
  }
}

export function lsSet<T>(key: string, value: T, type: 'string' | 'number' | 'object') {
  switch (type) {
    case 'string':
      localStorage.setItem(key, value as unknown as string);
      break;
    case 'number':
      localStorage.setItem(key, (value as unknown as number).toString());
      break;
    case 'object':
      localStorage.setItem(key, JSON.stringify(value));
      break;
  }
}
