import { clsx } from 'clsx';

import { IconProps } from './';

export function ClaimBackIcon({ className }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="74.1"
      height="73.853"
      viewBox="0 0 74.1 73.853"
      className={clsx('text-white stroke-white', className)}
    >
      <g id="Group_2641" data-name="Group 2641" transform="translate(-658.311 -959.745)">
        <path
          id="Path_10481"
          data-name="Path 10481"
          d="M42.526,21.225A19.21,19.21,0,1,1,23.315,2.014,19.211,19.211,0,0,1,42.526,21.225Z"
          transform="translate(672.047 975.447)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_10482"
          data-name="Path 10482"
          d="M9.373,20.7a7.23,7.23,0,0,0,5.806,2.448c3.584,0,6.49-1.982,6.49-4.425s-3.113-4.085-6.49-4.425h-.02c-3.1-.342-5.33-1.773-5.33-3.958s2.386-3.958,5.33-3.958a5.649,5.649,0,0,1,4.863,2.334"
          transform="translate(679.841 981.909)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <line
          id="Line_585"
          data-name="Line 585"
          y1="20.843"
          transform="translate(694.986 986.319)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_10483"
          data-name="Path 10483"
          d="M0,7.046l7.249.139L6.275,0"
          transform="matrix(0.719, -0.695, 0.695, 0.719, 689.422, 1024.347)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_10484"
          data-name="Path 10484"
          d="M7.249.139,0,0,.975,7.185"
          transform="matrix(0.719, -0.695, 0.695, 0.719, 691.096, 968.864)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_10485"
          data-name="Path 10485"
          d="M0,0A28.1,28.1,0,0,1,11.9,22.966,28.1,28.1,0,0,1,0,45.932"
          transform="matrix(0.719, -0.695, 0.695, 0.719, 691.097, 968.864)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeWidth="1"
        />
        <path
          id="Path_10486"
          data-name="Path 10486"
          d="M11.9,0a28.1,28.1,0,0,0,0,45.932"
          transform="matrix(0.719, -0.695, 0.695, 0.719, 659.155, 999.709)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeWidth="1"
        />
      </g>
    </svg>
  );
}
