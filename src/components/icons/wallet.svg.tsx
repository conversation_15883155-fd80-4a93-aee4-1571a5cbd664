import { clsx } from 'clsx';

import { IconProps } from './';

export function WalletIcon({ className }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="62.954"
      height="98.148"
      viewBox="0 0 62.954 98.148"
      className={clsx('text-white stroke-white', className)}
    >
      <g data-name="Group 1426" transform="translate(0 0)">
        <g data-name="Group 1427" transform="translate(0.5 0.5)">
          <path
            data-name="Path 3372"
            d="M55.495,54.43V72.044a1.937,1.937,0,0,1-1.931,1.931H3.855"
            transform="translate(0.973 23.173)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3373"
            d="M3.855,32.129h49.7a1.938,1.938,0,0,1,1.935,1.931V53.236"
            transform="translate(0.973 13.383)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3374"
            d="M46.238,54.43V65.486a1.4,1.4,0,0,1-1.4,1.4H8.782"
            transform="translate(3.135 23.173)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3375"
            d="M8.782,37.056H44.838a1.4,1.4,0,0,1,1.4,1.4v12.5"
            transform="translate(3.135 15.546)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3376"
            d="M34.541,29.551H40.09a1.937,1.937,0,0,1,1.931,1.931V35.19"
            transform="translate(14.443 12.252)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3377"
            d="M31.176,25.422h2.911a1.935,1.935,0,0,1,1.928,1.931v4.006"
            transform="translate(12.965 10.439)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3378"
            d="M5.327,69.485A4.826,4.826,0,0,1,.5,64.662v0"
            transform="translate(-0.5 27.663)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3379"
            d="M5.328,35.073a4.826,4.826,0,1,1,0-9.651h0"
            transform="translate(-0.5 10.439)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3380"
            d="M6.409,30.033a4.823,4.823,0,0,1-3.571-1.581"
            transform="translate(0.526 11.769)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 413"
            y1="51.636"
            transform="translate(0.002 40.685)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 414"
            x2="3.076"
            transform="translate(4.828 35.861)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3381"
            d="M51.35,52.289a5.493,5.493,0,0,1-5.493,5.493h-14.5A5.492,5.492,0,0,1,31.221,46.8c.046,0,.091,0,.137,0h14.5A5.491,5.491,0,0,1,51.35,52.289Z"
            transform="translate(10.604 19.822)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <circle
            data-name="Ellipse 106"
            cx="2.235"
            cy="2.235"
            r="2.235"
            transform="translate(53.384 69.876)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3382"
            d="M6.145,27.174,3.677,3.923,20.541,2.135"
            transform="translate(0.894 0.217)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3383"
            d="M17.338,7.235a3.831,3.831,0,0,1-.483-1.5l-6.118.648a3.832,3.832,0,0,1-3.4,4.216h0L8.874,25.128"
            transform="translate(2.498 1.799)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 415"
            x2="1.531"
            y2="14.413"
            transform="translate(7.039 27.391)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 416"
            x1="1.286"
            y1="12.135"
            transform="translate(11.374 26.93)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3384"
            d="M11.289,22.84A4.325,4.325,0,0,1,12.952,15.9"
            transform="translate(3.746 6.26)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3385"
            d="M12.01,23.237,17.447.5,32.3,4.05"
            transform="translate(4.553 -0.5)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3386"
            d="M28.392,6.457l-5.421-1.3A3.834,3.834,0,0,1,18.356,8l-3.4,14.21"
            transform="translate(5.847 1.545)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 417"
            x1="4.556"
            y2="19.066"
            transform="translate(12.007 22.737)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 418"
            y1="12.126"
            x2="2.898"
            transform="translate(17.905 23.752)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3387"
            d="M16.791,20.587a4.326,4.326,0,0,1,2.963-5.351l.012,0"
            transform="translate(6.577 5.967)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3388"
            d="M18.842,21.519,30.662,1.352,50.1,12.742,38.282,32.912"
            transform="translate(7.551 -0.127)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3389"
            d="M33.373,28.146,40.76,15.54A3.832,3.832,0,0,1,39.393,10.3L34.086,7.185a3.829,3.829,0,0,1-5.241,1.367l-7.389,12.61"
            transform="translate(8.699 2.434)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 419"
            y1="9.018"
            x2="5.284"
            transform="translate(40.55 32.784)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3390"
            d="M22.448,15.367,10.629,35.534l.414.243"
            transform="translate(3.946 6.025)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3391"
            d="M18.3,35.1l-.609-.357A3.829,3.829,0,0,0,16.323,29.5h0L23.709,16.9"
            transform="translate(6.446 6.698)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 420"
            x1="6.576"
            y2="11.222"
            transform="translate(35.497 30.581)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3392"
            d="M24.729,24.373a4.323,4.323,0,1,1,5.917-1.544A4.323,4.323,0,0,1,24.729,24.373Z"
            transform="translate(9.197 6.444)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 421"
            x2="42.048"
            transform="translate(6.936 41.802)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
      </g>
    </svg>
  );
}
