import { clsx } from 'clsx';

import { IconProps } from './';

export function FeeIcon({ className, size }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size || 85}
      height={size || 85}
      viewBox="0 0 85 85"
      className={clsx('text-white stroke-white', className)}
    >
      <circle
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        cx="36.8"
        cy="24"
        r="19.9"
      />
      <path
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M21,22.5c0.2-1.4,0.4-2.9,1-4.1"
      />
      <path
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M23.6,14.7c2.5-3.5,6.4-6,10.9-6.6"
      />
      <path
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M36.8,4.1h5.7c10.9,0,19.9,8.8,19.9,19.9s-8.8,19.9-19.9,19.9h-5.7"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="56.5"
        y1="26.8"
        x2="62.2"
        y2="26.8"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="56.5"
        y1="21.1"
        x2="62.2"
        y2="21.1"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="54.6"
        y1="15.4"
        x2="60.4"
        y2="15.4"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="50.7"
        y1="9.8"
        x2="56.5"
        y2="9.8"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="54.6"
        y1="32.4"
        x2="60.4"
        y2="32.4"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="50.7"
        y1="38.1"
        x2="56.5"
        y2="38.1"
      />
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M16.6,72c0,0,14,0,18.1,0.4c2.1,0.2,6.4-0.4,9.9-1.6c3.5-1.2,22.2-7.6,24.8-8.4c2.9-0.8,2.1-4.9-2.3-3.9
    c-4.3,1-16.6,4.3-16.6,4.3"
      />
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M60.1,60.3c0.4-0.2,0.6-0.4,1-0.6c1.2-0.6,1.6-2.5,2.1-4.3c0.4-1.8,0.6-2.5,1-4.3c0.4-1.6-3.1-3.1-3.9,0.4
    c-0.8,3.5-1.4,5.1-3.1,6.2c-1.2,0.8-4.5,2.9-6.6,4.1"
      />
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M55,59.1c0.4-1,0.4-2.5,0.4-3.9c0-2.1,0-2.5,0-4.3c0-1.8-3.7-2.3-3.7,1.2c0,3.7-0.2,5.3-1.4,6.8
    c-0.2,0.2-0.2,0.2-0.4,0.4"
      />
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M21.2,72c-0.2,3.9-1,6.6-2.3,6.6s-2.5-4.3-2.5-9.6c0-5.3,0.8-9.6,2.3-9.6c0,0,5.7,0.6,8.6,1s4.3-2.7,9.9-2.3
    s10.3,1,11.9,1.4c1.6,0.4,2.7,3.9,0,4.7c-3.3,1-5.5-1-15,2.9"
      />
      <circle
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        cx="14.4"
        cy="75.5"
        r="0.2"
      />
      <line
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="18.7"
        y1="59.3"
        x2="14.2"
        y2="58.9"
      />
      <line
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="18.9"
        y1="78.6"
        x2="13.2"
        y2="78.8"
      />
      <path
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        d="M31,30.3c1.2,1.6,3.5,2.7,6.4,2.7c3.9,0,7-2.1,7-4.7s-3.3-4.3-7-4.7l0,0c-3.3-0.4-5.7-1.8-5.7-4.3
    s2.7-4.3,5.7-4.3c2.3,0,4.3,1,5.3,2.5"
      />
      <line
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
        x1="37.2"
        y1="35.3"
        x2="37.2"
        y2="12.7"
      />
    </svg>
  );
}
