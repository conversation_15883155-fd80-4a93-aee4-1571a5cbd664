import { clsx } from 'clsx';

import { IconProps } from './';

export function CheckIcon({ className }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="10.213"
      height="7.807"
      viewBox="0 0 10.213 7.807"
      className={clsx('text-white stroke-white', className)}
    >
      <path
        id="Path_3366"
        data-name="Path 3366"
        d="M5727.1-14333.229l2.746,3.045,6.055-6.6"
        transform="translate(-5726.394 14337.491)"
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1"
      />
    </svg>
  );
}
