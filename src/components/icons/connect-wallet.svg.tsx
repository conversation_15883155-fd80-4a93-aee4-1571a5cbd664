import { clsx } from 'clsx';

import { IconProps } from './';

export function ConnectWalletIcon({ className }: IconProps) {
  return (
    <svg
      data-name="Group 1488"
      xmlns="http://www.w3.org/2000/svg"
      width="97.385"
      height="67.782"
      viewBox="0 0 97.385 67.782"
      className={clsx('text-white stroke-white', className)}
    >
      <g data-name="Group 1489" transform="translate(0.5 0.5)">
        <path
          data-name="Path 3475"
          d="M25.093,44.169l-1.473,1.473a3.047,3.047,0,1,1-4.31-4.309l5.064-5.066A3.047,3.047,0,0,1,29.546,38"
          transform="translate(5.799 11.761)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          data-name="Path 3476"
          d="M28.069,34.157l1.473-1.473a3.047,3.047,0,0,1,4.31,4.309l-5.064,5.066a3.047,3.047,0,0,1-5.172-1.73"
          transform="translate(7.627 10.501)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <rect
          data-name="Rectangle 1474"
          width="52.526"
          height="32.592"
          rx="3.895"
          transform="translate(0 34.19)"
          fill="none"
          strokeWidth="1"
        />
        <path
          data-name="Path 3477"
          d="M49.156,48.228H81.4A11.617,11.617,0,1,0,77.426,25.7c.011-.238.036-.472.036-.711A15.527,15.527,0,0,0,61.936,9.466H61.9a17.878,17.878,0,0,0-33.239,6.7c-.362-.024-.724-.055-1.093-.055a16.066,16.066,0,0,0-15.9,18.334"
          transform="translate(3.371 -0.5)"
          fill="none"
          strokeWidth="1"
        />
        <path
          data-name="Path 3478"
          d="M11.715,34.67H.5v8.607H11.715a4.3,4.3,0,0,0,0-8.607Z"
          transform="translate(-0.5 11.513)"
          fill="none"
          strokeWidth="1"
        />
        <circle
          data-name="Ellipse 114"
          cx="1.687"
          cy="1.687"
          r="1.687"
          transform="translate(9.479 48.799)"
          fill="none"
          strokeWidth="1"
        />
      </g>
    </svg>
  );
}
