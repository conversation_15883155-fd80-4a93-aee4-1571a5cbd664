import { clsx } from 'clsx';

import { IconProps } from './';

export function StatusConnectedIcon({ className }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="145.019"
      height="80.198"
      viewBox="0 0 145.019 80.198"
      className={clsx('text-white stroke-white', className)}
    >
      <g data-name="Group 1432" transform="translate(0.5 0.5)">
        <g data-name="Group 1385" transform="translate(23.14 41.076)">
          <path
            data-name="Path 3251"
            d="M101.172,35.184V53.328a8.826,8.826,0,0,1-8.827,8.826H70.155"
            transform="translate(-10.638 -35.112)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3252"
            d="M101.192,35.124V38.1a5.306,5.306,0,0,0,5.306,5.306h3.082"
            transform="translate(-4.769 -35.124)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <circle
            data-name="Ellipse 100"
            cx="2.486"
            cy="2.486"
            r="2.486"
            transform="translate(104.811 5.795)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3253"
            d="M91.642,35.124v7.985a5.306,5.306,0,0,1-5.306,5.306H85.222"
            transform="translate(-7.789 -35.124)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <circle
            data-name="Ellipse 101"
            cx="2.486"
            cy="2.486"
            r="2.486"
            transform="translate(72.461 10.804)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3254"
            d="M51.018,61.291H28.93A8.826,8.826,0,0,1,20.1,52.464V39.749"
            transform="translate(-20.104 -34.249)"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <g data-name="Group 1386" transform="translate(0 0)">
          <rect
            data-name="Rectangle 759"
            width="46.623"
            height="28.928"
            rx="3.895"
            transform="translate(0 17.711)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <rect
            data-name="Rectangle 760"
            width="46.623"
            height="35.96"
            rx="3.895"
            transform="translate(0 10.711)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <rect
            data-name="Rectangle 761"
            width="46.623"
            height="42.994"
            rx="3.895"
            transform="translate(0 3.677)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 358"
            y2="3.212"
            transform="translate(5.188 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 359"
            y2="3.212"
            transform="translate(8.813 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 360"
            y2="3.212"
            transform="translate(12.437 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 361"
            y2="3.212"
            transform="translate(16.062 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 362"
            y2="3.212"
            transform="translate(19.687 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 363"
            y2="3.212"
            transform="translate(23.312 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 364"
            y2="3.212"
            transform="translate(26.936 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 365"
            y2="3.212"
            transform="translate(30.524 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 366"
            y2="3.212"
            transform="translate(34.144 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 367"
            y2="3.212"
            transform="translate(37.765 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <line
            data-name="Line 368"
            y2="3.212"
            transform="translate(41.386 39.589)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3255"
            d="M47.122,23.235H32.4a2.4,2.4,0,0,0-2.151,1.249,7.246,7.246,0,0,1-12.878,0,2.4,2.4,0,0,0-2.15-1.249H.5"
            transform="translate(-0.5 3.765)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <path
            data-name="Path 3256"
            d="M122.76,23.474A8.61,8.61,0,0,0,119.8,24c.008-.177.027-.351.027-.529a11.564,11.564,0,0,0-11.563-11.563h-.024A13.313,13.313,0,0,0,83.493,16.9c-.27-.018-.54-.04-.815-.04a11.959,11.959,0,1,0,0,23.918H122.76a8.651,8.651,0,0,0,0-17.3Z"
            transform="translate(12.608 0.396)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
          <g data-name="Group 1389" transform="translate(8.224)">
            <line
              data-name="Line 355"
              x2="4.214"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <line
              data-name="Line 356"
              y2="8.859"
              transform="translate(42.326 20.734)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <line
              data-name="Line 357"
              y2="3.375"
              transform="translate(42.326 32.9)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <line
              data-name="Line 369"
              x2="4.366"
              transform="translate(89.132 38.153)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <line
              data-name="Line 370"
              x2="14.199"
              transform="translate(96.363 38.153)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <path
              data-name="Path 3257"
              d="M105.158,13.941a8.606,8.606,0,0,1,5.187,7.887"
              transform="translate(10.897 2.022)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
            <path
              data-name="Path 3258"
              d="M92.573,7.734a10.392,10.392,0,0,1,8.965,5.192"
              transform="translate(8.551 0.869)"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1"
            />
          </g>
          <path
            data-name="Path 3259"
            d="M59.582,71.341,48.5,60.96l3.588-3.831L59.11,63.7l13.166-15.81,4.034,3.364Z"
            transform="translate(9.432 7.856)"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
      </g>
    </svg>
  );
}
