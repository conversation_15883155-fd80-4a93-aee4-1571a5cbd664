import { clsx } from 'clsx';

import { IconProps } from './';

export function ClaimIcon({ className }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="56.123"
      height="74.592"
      viewBox="0 0 56.123 74.592"
      className={clsx('text-white stroke-white', className)}
    >
      <g id="Group_2644" data-name="Group 2644" transform="translate(-1188.433 -547.363)">
        <path
          id="Path_3393"
          data-name="Path 3393"
          d="M5.814,1V22.482H1L2.754,1Z"
          transform="translate(1187.933 598.969)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_3394"
          data-name="Path 3394"
          d="M19.353,6.426c3.663-1.453,10.281-2.533,14.639.4,3.132.764,9.143,1.88,12.125,1.566s4.035,3.107-.942,4.238a51.924,51.924,0,0,1-10.909.707"
          transform="translate(1179.626 597.172)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_3395"
          data-name="Path 3395"
          d="M47.755,10.052c4.369-1.218,8.3-2.269,12.453-3.841C63.141,5.1,65.231,7.846,61.7,10.09,56.906,13.134,44.719,19.9,41.685,20.921c-2.3.772-7.9-.974-13.971-2.2-6.27-1.264-10.177,1.335-10.177,1.335"
          transform="translate(1180.448 596.726)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_3396"
          data-name="Path 3396"
          d="M13.823,22.238H9.794V3.607h5.233Z"
          transform="translate(1183.952 597.789)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_3397"
          data-name="Path 3397"
          d="M5.814,22.485H1L2.755,1H5.814Z"
          transform="translate(1187.933 598.969)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <line
          id="Line_422"
          data-name="Line 422"
          x2="2.093"
          transform="translate(1193.746 617.202)"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_176"
          data-name="Path 176"
          d="M541.066,616.9a20.837,20.837,0,0,1-18.2,20.676,19.463,19.463,0,0,1-2.645.176,20.038,20.038,0,0,1-3.7-.333A20.846,20.846,0,1,1,541.066,616.9Z"
          transform="translate(696.247 -48.19)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_177"
          data-name="Path 177"
          d="M519.715,601.779a17.323,17.323,0,0,1,3.49-.847"
          transform="translate(690.912 -49.47)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <path
          id="Path_178"
          data-name="Path 178"
          d="M504.516,615.713a17.413,17.413,0,0,1,7.149-10.8"
          transform="translate(694.898 -50.513)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <g
          id="_Radial_Repeat_"
          data-name="&lt;Radial Repeat&gt;"
          transform="translate(1232.71 569.92)"
        >
          <line
            id="Line_93"
            data-name="Line 93"
            x1="1.697"
            y1="0.126"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <g
          id="_Radial_Repeat_2"
          data-name="&lt;Radial Repeat&gt;"
          transform="translate(1231.899 573.921)"
        >
          <line
            id="Line_94"
            data-name="Line 94"
            x1="1.612"
            y1="0.544"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <g
          id="_Radial_Repeat_3"
          data-name="&lt;Radial Repeat&gt;"
          transform="translate(1230.118 577.596)"
        >
          <line
            id="Line_95"
            data-name="Line 95"
            x1="1.426"
            y1="0.928"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <g
          id="_Radial_Repeat_4"
          data-name="&lt;Radial Repeat&gt;"
          transform="translate(1227.479 580.711)"
        >
          <line
            id="Line_96"
            data-name="Line 96"
            x1="1.15"
            y1="1.253"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <g
          id="_Radial_Repeat_5"
          data-name="&lt;Radial Repeat&gt;"
          transform="translate(1224.148 583.073)"
        >
          <line
            id="Line_97"
            data-name="Line 97"
            x1="0.802"
            y1="1.5"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1"
          />
        </g>
        <path
          id="Path_179"
          data-name="Path 179"
          d="M519.133,628.721a7.34,7.34,0,0,0,6.209,2.98c3.832,0,6.938-2.411,6.938-5.385s-3.329-4.97-6.938-5.385-6.207-2.156-6.207-4.817,2.779-4.818,6.207-4.818A6.485,6.485,0,0,1,531,614.135"
          transform="translate(691.064 -52.188)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <line
          id="Line_98"
          data-name="Line 98"
          y1="2.754"
          transform="translate(1216.216 556.352)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <line
          id="Line_99"
          data-name="Line 99"
          y2="0.79"
          transform="translate(1216.216 581.477)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
        <line
          id="Line_100"
          data-name="Line 100"
          y1="17.76"
          transform="translate(1216.216 561.752)"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        />
      </g>
    </svg>
  );
}
