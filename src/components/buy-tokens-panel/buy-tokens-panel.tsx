import { Membership, Round } from '@/class/interface/presale';

import { PresaleTimer } from '../vest-panel/presale-timer';
import { RememberFee } from '../vest-panel/remember-fee';
import { WalletDisconnect } from '../vest-panel/wallet-disconnect';
import { WalletStatus } from '../vest-panel/wallet-status';
import PurchaseForm from './purchase-form';

interface BuyTokensPanelProps {
  membership: Membership;
  round: Round;
}

export default function BuyTokensPanel({ membership, round }: BuyTokensPanelProps) {
  return (
    <>
      <PresaleTimer round={round} />

      <div className="grid grid-cols-2 gap-4 xl:grid-cols-3 xl:grid-rows-3 w-full grid-areas-presale-slim xl:grid-areas-presale-wide">
        <PurchaseForm membership={membership} round={round} />

        <WalletStatus className="xl:row-span-2 order-1 xl:order-[unset]" />

        <RememberFee isRefundable={false} />

        <WalletDisconnect />
      </div>
    </>
  );
}
