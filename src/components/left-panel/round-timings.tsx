import { useEffect, useState } from 'react';

import { Membership, PresaleRoundState, Round } from '@/class/interface/presale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatTimePad } from '@/lib/format-time-pad';

interface RoundTimingsProps {
  round: Round;
  membership: Membership;
}

export default function RoundTimings({ round, membership }: RoundTimingsProps) {
  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  };

  const getNextEvent = () => {
    const now = currentTime / 1000;

    // Check what's the next upcoming event
    if (now < round.startTimestamp) {
      return { label: 'Round starts in', timestamp: round.startTimestamp };
    } else if (now < round.endTimestamp) {
      return { label: 'Round ends in', timestamp: round.endTimestamp };
    } else if (now < round.listingTimestamp) {
      return { label: 'TGE starts in', timestamp: round.listingTimestamp };
    } else if (
      round.state === PresaleRoundState.vesting &&
      membership.nextUnlockTimestamp > 0 &&
      now < membership.nextUnlockTimestamp
    ) {
      return { label: 'Next unlock in', timestamp: membership.nextUnlockTimestamp };
    } else if (round.refundsEndTimestamp > 0 && now < round.refundsEndTimestamp) {
      return { label: 'Refunds end in', timestamp: round.refundsEndTimestamp };
    }

    return null;
  };

  const formatCountdown = (targetTimestamp: number) => {
    const now = currentTime / 1000;
    const diff = targetTimestamp - now;

    if (diff <= 0) return null;

    const days = Math.floor(diff / 86400);
    const hours = Math.floor((diff % 86400) / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    const seconds = Math.floor(diff % 60);

    return { days, hours, minutes, seconds };
  };

  const nextEvent = getNextEvent();
  const countdown = nextEvent ? formatCountdown(nextEvent.timestamp) : null;

  const getStateLabel = (state: number) => {
    switch (state) {
      case PresaleRoundState.pending:
        return 'Pending';
      case PresaleRoundState.active:
        return 'Active';
      case PresaleRoundState.vesting:
        return 'Vesting';
      default:
        return 'Unknown';
    }
  };

  const getStateColor = (state: number) => {
    switch (state) {
      case PresaleRoundState.pending:
        return 'text-yellow-400';
      case PresaleRoundState.active:
        return 'text-green-400';
      case PresaleRoundState.vesting:
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const now = currentTime / 1000;

  return (
    <Card className="w-full">
      <CardContent className="space-y-2">
        {/* Live Countdown */}
        {nextEvent && countdown && (
          <div className="flex justify-between items-center m-3">
            <span className="text-sm text-gray-400">{nextEvent.label}:</span>
            <span className="text-sm font-mono">
              {countdown.days > 0 && `${countdown.days}d `}
              {formatTimePad(countdown.hours)}:{formatTimePad(countdown.minutes)}:
              {formatTimePad(countdown.seconds)}
            </span>
          </div>
        )}

        {/* Round Status */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-400">Status:</span>
          <span className={`text-sm font-medium ${getStateColor(round.state)}`}>
            {getStateLabel(round.state)}
          </span>
        </div>

        {/* Round Start */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Round Start:</span>
            <span
              className={`text-xs ${now >= round.startTimestamp ? 'text-green-400' : 'text-gray-300'}`}
            >
              {now >= round.startTimestamp ? '✓' : '⏳'}
            </span>
          </div>
          <p className="text-xs text-gray-300">{formatDate(round.startTimestamp)}</p>
        </div>

        {/* Round End */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Round End:</span>
            <span
              className={`text-xs ${now >= round.endTimestamp ? 'text-green-400' : 'text-gray-300'}`}
            >
              {now >= round.endTimestamp ? '✓' : '⏳'}
            </span>
          </div>
          <p className="text-xs text-gray-300">{formatDate(round.endTimestamp)}</p>
        </div>

        {/* TGE Start */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">TGE Start:</span>
            <span
              className={`text-xs ${now >= round.listingTimestamp ? 'text-green-400' : 'text-gray-300'}`}
            >
              {now >= round.listingTimestamp ? '✓' : '⏳'}
            </span>
          </div>
          <p className="text-xs text-gray-300">{formatDate(round.listingTimestamp)}</p>
        </div>

        {/* Listing */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Listing:</span>
            <span
              className={`text-xs ${now >= round.listingTimestamp ? 'text-green-400' : 'text-gray-300'}`}
            >
              {now >= round.listingTimestamp ? '✓' : '⏳'}
            </span>
          </div>
          <p className="text-xs text-gray-300">{formatDate(round.listingTimestamp)}</p>
        </div>

        {/* Refunds End */}
        {round.refundsEndTimestamp > 0 && (
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Refunds End:</span>
              <span
                className={`text-xs ${now >= round.refundsEndTimestamp ? 'text-red-400' : 'text-yellow-400'}`}
              >
                {now >= round.refundsEndTimestamp ? '✗' : '⏳'}
              </span>
            </div>
            <p className="text-xs text-gray-300">
              {formatDate(round.refundsEndTimestamp)}
            </p>
          </div>
        )}

        {/* Next Unlock (if in vesting) */}
        {round.state === PresaleRoundState.vesting &&
          membership.nextUnlockTimestamp > 0 && (
            <div className="space-y-1 border-t border-gray-700 pt-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Next Unlock:</span>
                <span
                  className={`text-xs ${now >= membership.nextUnlockTimestamp ? 'text-green-400' : 'text-blue-400'}`}
                >
                  {now >= membership.nextUnlockTimestamp ? '✓' : '⏳'}
                </span>
              </div>
              <p className="text-xs text-gray-300">
                {formatDate(membership.nextUnlockTimestamp)}
              </p>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
