import { Round } from '@/class/interface/presale';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface RoundSelectProps {
  rounds: Round[];
  selectedRoundId: number;
  setSelectedRoundId: (round: number) => void;
}

export function RoundSelect({
  rounds,
  selectedRoundId,
  setSelectedRoundId,
}: RoundSelectProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="round" className="text-white">
        Round
      </Label>

      <Select
        value={selectedRoundId?.toString()}
        onValueChange={(value) => {
          setSelectedRoundId(parseInt(value));
        }}
      >
        <SelectTrigger className="w-24 text-white">
          <SelectValue />
        </SelectTrigger>

        <SelectContent className="text-white bg-[#202020]">
          {rounds.map((round) => (
            <SelectItem key={round.roundId.toString()} value={round.roundId.toString()}>
              {round.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
