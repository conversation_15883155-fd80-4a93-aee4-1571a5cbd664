import { formatUnits } from 'viem';

import { cutDecimals } from '@/lib/cut-decimals';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

interface NextUnlockProps {
  value: string;
  timestamp: number;
  tokenDecimals: number;
}

export const NextUnlock = ({ value, timestamp, tokenDecimals }: NextUnlockProps) => {
  const unlockDateString = new Date(timestamp * 1000).toLocaleDateString('UTC', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour12: true,
    hour: 'numeric',
    minute: 'numeric',
  });

  return (
    <Card className="col-span-2 xl:col-span-1">
      <CardHeader>
        <CardTitle>Unlock</CardTitle>
        {timestamp > 0 && timestamp * 1000 > new Date().getTime() && (
          <CardDescription>{unlockDateString}</CardDescription>
        )}
      </CardHeader>
      <CardContent className="h-full">
        <span className="text-2xl xl:text-4xl font-semibold text-white">
          {timestamp > 0
            ? cutDecimals(formatUnits(BigInt(value), tokenDecimals), 2)
            : 'TBA'}
        </span>
      </CardContent>
    </Card>
  );
};
