import { useDisconnect } from 'wagmi';

import { lsSet } from '@/lib/local-storage';

import { DisconnectWalletIcon } from '../icons';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

export function WalletDisconnect() {
  // just for testing
  const { disconnect } = useDisconnect();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Connected</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <DisconnectWalletIcon />
        <Button
          variant={'outline'}
          onClick={() => {
            disconnect();
            lsSet('wagmiConnected', '', 'string');
          }}
        >
          disconnect
        </Button>
      </CardContent>
    </Card>
  );
}
