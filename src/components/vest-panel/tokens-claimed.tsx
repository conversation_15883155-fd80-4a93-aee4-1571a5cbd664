import { formatUnits } from 'viem';

import { cutDecimals } from '@/lib/cut-decimals';

import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';

interface TokensClaimedProps {
  current: string;
  max: string;
  tokenDecimals: number;
}

export const TokensClaimed = ({ current, max, tokenDecimals }: TokensClaimedProps) => {
  const claimedPercentage =
    (Number(current.replaceAll(' ', '')) / (Number(max.replaceAll(' ', '')) || 1)) * 100;

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Tokens claimed</CardTitle>
      </CardHeader>
      <CardContent className="w-full">
        <h2 className="text-2xl xl:text-4xl font-semibold text-white">
          {cutDecimals(formatUnits(BigInt(current), tokenDecimals), 2)} of{' '}
          {cutDecimals(formatUnits(BigInt(max), tokenDecimals), 2)}
        </h2>
        <Progress value={claimedPercentage} />
      </CardContent>
    </Card>
  );
};
