import { formatUnits } from 'viem';

import { cutDecimals } from '@/lib/cut-decimals';

import { WalletIcon } from '../icons';
import { Spinner } from '../icons/spinner';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface YouOwnProps {
  balance: string;
  symbol: string;
  decimals: number;
}

export const YouOwn = ({ balance, symbol, decimals }: YouOwnProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>You own</CardTitle>
      </CardHeader>
      <CardContent>
        <WalletIcon />
        {balance === undefined ? (
          <Spinner className="animate-spin" />
        ) : (
          <div className="flex flex-col gap-2 text-white">
            <span className="text-2xl xl:text-4xl font-semibold">
              {cutDecimals(formatUnits(BigInt(balance), decimals), 2)}
            </span>
            <span className="text-xl xl:text-2xl font-semibold">{symbol}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
