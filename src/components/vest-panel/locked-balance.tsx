import { formatUnits } from 'viem';

import { cutDecimals } from '@/lib/cut-decimals';

import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface LockedBalanceProps {
  locked: string;
  tokenDecimals: number;
}

export const LockedBalance = ({ locked, tokenDecimals }: LockedBalanceProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Locked balance</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <span className="text-2xl xl:text-4xl font-semibold text-white">
          {cutDecimals(formatUnits(BigInt(locked), tokenDecimals), 2)}
        </span>
      </CardContent>
    </Card>
  );
};
