import { useAccount } from 'wagmi';

import { addressShortener } from '@/lib/address-shortener';

import { StatusConnectedIcon } from '../icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface WalletStatusProps {
  className?: string;
}

export function WalletStatus({ className }: WalletStatusProps) {
  const { address } = useAccount();
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Wallet status</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <StatusConnectedIcon />
        <div>
          <span className="uppercase text-lg xl:text-2xl font-semibold">connected</span>

          {address && <p className="text-white">{addressShortener(address)}</p>}
        </div>
      </CardContent>
    </Card>
  );
}
