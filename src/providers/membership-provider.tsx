import { createContext, useContext, useEffect, useState } from 'react';
// import { Address } from 'viem';
import { useAccount } from 'wagmi';

import { Membership } from '@/class/interface/presale';

import { PresaleContext } from './presale-provider';

interface MembershipsContextProps {
  memberships: Membership[];
  selectedMembershipId: string;
  handleMembershipChange: (membershipId: string) => void;
  fetchMemberships: () => void;
  loading: boolean;
}

export const MembershipsContext = createContext<MembershipsContextProps>({
  memberships: [],
  selectedMembershipId: '',
  handleMembershipChange: () => {},
  fetchMemberships: () => {},
  loading: true,
});

interface MembershipsProviderProps {
  children: React.ReactNode;
}

export const MembershipsProvider = ({ children }: MembershipsProviderProps) => {
  const { presaleInstance, selectedRoundId, handleRoundChange } =
    useContext(PresaleContext);

  const { address } = useAccount();

  const [selectedMembershipId, setSelectedMembershipId] = useState<string>('');

  const [memberships, setMemberships] = useState<Membership[]>([]);
  const [loading, setLoading] = useState(!selectedMembershipId);

  const handleMembershipChange = (membershipId: string) => {
    setSelectedMembershipId(membershipId);
  };

  const fetchMemberships = async () => {
    if (presaleInstance && address) {
      const memberships = await presaleInstance.getMemberships(
        // '0x4e1a2d824e7B8d6454e94607BF5772669d39a2D0' as Address,
        address,
      );

      if (memberships.length <= 0) {
        setSelectedMembershipId('');
        setLoading(false);
        return;
      }

      const roundMemberships = memberships.filter((m) => m.roundId === selectedRoundId);

      if (
        roundMemberships.length > 0 &&
        (!selectedMembershipId ||
          !roundMemberships.some((m) => m.id === selectedMembershipId))
      ) {
        handleMembershipChange(roundMemberships[0].id);
      }

      if (
        roundMemberships.length <= 0 &&
        (!selectedMembershipId || !memberships.some((m) => m.id === selectedMembershipId))
      ) {
        handleRoundChange(memberships[0].roundId);
        handleMembershipChange(memberships[0].id);
      }

      setMemberships(memberships);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMemberships();
  }, [presaleInstance, address]);

  return (
    <MembershipsContext.Provider
      value={{
        memberships,
        selectedMembershipId,
        handleMembershipChange,
        fetchMemberships,
        loading,
      }}
    >
      {children}
    </MembershipsContext.Provider>
  );
};
