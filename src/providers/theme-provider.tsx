import { createContext, useContext } from 'react';

import { ProjectConfigContext } from './project-config-provider';

export type Color = `#${string}`;

export interface ThemeContextProps {
  background?: Color;
  text?: Color;
  accent?: Color;
  header?: {
    background?: Color;
    text?: Color;
  };
  footer?: {
    background?: Color;
    text?: Color;
  };
}

export const ThemeContext = createContext<ThemeContextProps | undefined>({
  background: '#fff',
  text: '#000',
  accent: '#000',
  header: {
    background: '#fff',
    text: '#000',
  },
  footer: {
    background: '#fff',
    text: '#000',
  },
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const { colors } = useContext(ProjectConfigContext);

  return <ThemeContext.Provider value={colors}>{children}</ThemeContext.Provider>;
};
