import { createContext } from 'react';
import { Address, Chain } from 'viem';

import { PresaleVersion } from '@/class/presale-factory';

import { ThemeContextProps } from './theme-provider';

export type ProjectConfig = {
  walletConnectProjectId: string;
  name: string;
  chain: Chain;
  vestedToken: {
    name: string;
    symbol: string;
    decimals: number;
    address: Address;
    logo?: string;
  };
  collectedToken: {
    isNative?: boolean;
    symbol: string;
    decimals: number;
    address: Address;
  };
  presaleVersion: PresaleVersion;
  presaleAddress: Address;
  company: {
    name: string;
    website: string;
    logo: string;
  };
  colors?: ThemeContextProps;
};

export const ProjectConfigContext = createContext<ProjectConfig>({} as ProjectConfig);

interface ProjectConfigProviderProps {
  projectConfig: ProjectConfig;
  children: React.ReactNode;
}

export const ProjectConfigProvider = ({
  projectConfig,
  children,
}: ProjectConfigProviderProps) => {
  return (
    <ProjectConfigContext.Provider value={{ ...projectConfig }}>
      {children}
    </ProjectConfigContext.Provider>
  );
};
