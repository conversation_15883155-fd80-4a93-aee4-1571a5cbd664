import { createContext, useContext } from 'react';
import { createPublicClient, http, PublicClient } from 'viem';

import { ProjectConfigContext } from './project-config-provider';

export interface PublicClientContextProps {
  publicClient: PublicClient;
}

export const PublicClientContext = createContext<PublicClientContextProps>({
  publicClient: {} as PublicClient,
});

export const PublicClientProvider = ({ children }: { children: React.ReactNode }) => {
  const { chain } = useContext(ProjectConfigContext);

  const publicClient = createPublicClient({
    chain: chain,
    transport: http(),
    batch: {
      multicall: true,
    },
  });

  return (
    <PublicClientContext.Provider value={{ publicClient }}>
      {children}
    </PublicClientContext.Provider>
  );
};
