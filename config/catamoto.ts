import { base, bsc, foundry, mainnet } from 'viem/chains';

import { ProjectConfig } from '@/providers/project-config-provider';

export const config: ProjectConfig = {
  // Raiinmaker
  // walletConnectProjectId: '9df96a2808a7aac07d979cb9102f6359',
  // Tenset
  walletConnectProjectId: 'ff8ddbf4a743bdefef7bc7acbad40268',
  chain: bsc,
  // chain: {
  //   ...bsc,
  //   rpcUrls: {
  //     default: {
  //       http: ['https://rpc.phalcon.blocksec.com/rpc_19dc574eee134d5d8e690762912e2beb'],
  //     },
  //   },
  // },
  name: 'Catamo<PERSON>',
  vestedToken: {
    name: 'Catamo<PERSON>',
    symbol: 'CATA',
    decimals: 18,
    address: '******************************************',
    // @TODO add logo
    logo: 'https://catsale.catamoto.cat/assets/catamoto.jpeg',
  },
  collectedToken: {
    isNative: true,
    symbol: 'BNB',
    decimals: 18,
    address: '******************************************',
  },
  presaleVersion: 'v4',
  presaleAddress: '******************************************',
  company: {
    name: 'Catamoto',
    website: 'https://catamoto.cat/',
    logo: '/logo/catamoto.png',
  },
  colors: {
    background: '#25272c',
    text: '#fff',
    accent: '#ce2eef',
    header: {
      background: '#25272c',
    },
    footer: {
      background: '#25272c',
      text: '#fff',
    },
  },
};
