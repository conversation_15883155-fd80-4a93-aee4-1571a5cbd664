import { supportedChains } from '@/lib/wagmi/supported-chains';
import { ProjectConfig } from '@/providers/project-config-provider';

export const config: ProjectConfig = {
  // Tenset
  walletConnectProjectId: '0d93eac116d2b24f1b21df10c32fb4c6',
  chain: supportedChains.find((chain) => chain.id === 56)!,
  name: 'Solar<PERSON>',
  vestedToken: {
    name: 'Solar<PERSON>',
    symbol: 'SOLX',
    decimals: 18,
    address: '******************************************',
    // @TODO add logo
    logo: '',
  },
  collectedToken: {
    isNative: false,
    symbol: 'USDT',
    decimals: 18,
    address: '******************************************',
  },
  presaleVersion: 'v4',
  presaleAddress: '******************************************',
  company: {
    name: '<PERSON><PERSON>',
    website: 'https://solarx.ai/',
    logo: '/logo/solarx.svg',
  },
  colors: {
    background: '#010101',
    text: '#fff',
    accent: '#45D001',
    header: {
      background: '#010101',
    },
    footer: {
      background: '#010101',
      text: '#fff',
    },
  },
};
