import { foundry, mainnet } from 'viem/chains';

import { ProjectConfig } from '@/providers/project-config-provider';

export const config: ProjectConfig = {
  walletConnectProjectId: '123',
  chain: foundry,
  name: '<PERSON><PERSON><PERSON>',
  vestedToken: {
    name: '<PERSON><PERSON><PERSON>',
    symbol: 'ALVA',
    decimals: 18,
    address: '******************************************',
    logo: '/logo/alvara.png',
  },
  collectedToken: {
    symbol: 'USDT',
    decimals: 18,
    address: '******************************************',
  },
  presaleVersion: 'v3',
  presaleAddress: '******************************************',
  company: {
    name: 'Alvar<PERSON>',
    website: 'https://alvara.io',
    logo: '/logo/alvara.png',
  },
  colors: {
    background: '#0e172a',
    text: '#fff',
    accent: '#ce2eef',
    header: {
      background: '#331752',
    },
    footer: {
      background: '#0d0e23',
      text: '#fff',
    },
  },
};
