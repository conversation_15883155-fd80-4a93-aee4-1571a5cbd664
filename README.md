# Vest Portal - Unified Migration Platform

A React application for managing presale participation, token vesting, and fund migration, built with Vite, TypeScript, and Web3 integration.

## Features

- **Unified Migration Interface**: Single interface combining presale, vesting management, and fund migration functionality
- **Fund Migration**: Migrate funds from old presale contracts to new ones with a simple one-click process
- **Presale & Vesting Management**: View and manage your token allocations and vesting schedules
- **Multi-wallet Support**: Connect with MetaMask, WalletConnect, and other popular wallets
- **Multi-chain Support**: Works across different blockchain networks
- **Real-time Updates**: Live data updates for token balances and vesting schedules

## Routes

- `/` - Unified migrate interface with presale, vesting, and fund migration functionality
- `/migrate` - Redirects to main page for backward compatibility

## Technologies

Built with [Vite](https://github.com/vitejs/vite), [React](https://reactjs.org/), [TypeScript](https://www.typescriptlang.org/), [React Router](https://reactrouter.com/), [Wagmi](https://wagmi.sh/), [Viem](https://viem.sh/), and styled with [Tailwind CSS](https://tailwindcss.com/).

## Installation

Clone the repo and run `yarn install`

or Run command

```
npx degit TheSwordBreaker/vite-reactts-eslint-prettier project-name
```

## Start

After the successfull installation of the packages: `yarn dev`

## Steps in Vscode

#### (works with better with this template)

1. Install Eslint and prettier extension for vs code.
2. Make Sure Both are enabled
3. Make sure all packages are Installed. (Mostly Eslint and prettier in node_modules)
4. Enable formatOnSave of vs code
5. Open a .tsx file and check if the bottom right corners of vs code have Eslint and Prettier with a double tick

![Screenshot (253)_LI](https://user-images.githubusercontent.com/52120562/162486286-7383a737-d555-4f9b-a4dd-c4a81deb7b96.jpg)

If Everything is Good Then It Should Work, but let me new if something else happens

Made with ❤️ by theSwordBreaker(we Destory all types of sword ⚡)
